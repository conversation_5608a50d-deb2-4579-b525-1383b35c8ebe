<template>
  <div>
    <el-tree
        :data="gameNodes"
        :props="gameNodeProps"
        highlight-current
        :current-node-key="currentKey"
        node-key="name"
        @node-click="handleGameNodeClick"
    />
  </div>
</template>

<script>
export default {
  name: 'node-bar',
  data() {
    return {
      currentKey: null,
      gameNodes: [
        {
          name: 'scene_0'
        }
      ],
      gameNodeProps: {
        children: 'children',
        label: 'name',
      }
    }
  },
  methods: {
    setData(data) {
      this.gameNodes = data;
    },
    handleGameNodeClick(e) {
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
</style>