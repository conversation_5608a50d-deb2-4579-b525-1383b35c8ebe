<template>
  <div>
    <el-tree
        :data="gameNodes"
        :props="gameNodeProps"
        highlight-current
        :current-node-key="currentKey"
        node-key="name"
        @node-click="handleGameNodeClick"
    >
      <template #default="{ node, data }">
        <span class="tree-node">
          <i :class="getNodeIcon(node)" class="node-icon"></i>
          <span class="node-label">{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script>
export default {
  name: 'node-bar',
  data() {
    return {
      currentKey: null,
      gameNodes: [
        {
          name: 'scene_0'
        }
      ],
      gameNodeProps: {
        children: 'children',
        label: 'name',
      }
    }
  },
  methods: {
    setData(data) {
      this.gameNodes = data;
    },
    handleGameNodeClick(e) {
    },
    getNodeIcon(node) {
      // 第一层是场景图标，其他层级是普通节点图标
      if (node.level === 1) {
        return 'el-icon-monitor'; // 场景图标
      } else {
        return 'el-icon-document'; // 普通节点图标
      }
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.tree-node {
  display: flex;
  align-items: center;

  .node-icon {
    margin-right: 6px;
    font-size: 14px;
    color: #606266;
  }

  .node-label {
    font-size: 14px;
  }
}

// 场景图标颜色
.el-icon-monitor {
  color: #409EFF !important;
}

// 普通节点图标颜色
.el-icon-document {
  color: #909399 !important;
}
</style>